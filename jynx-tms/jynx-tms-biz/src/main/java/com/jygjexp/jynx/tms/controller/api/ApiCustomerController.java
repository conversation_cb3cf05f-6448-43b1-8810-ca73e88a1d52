package com.jygjexp.jynx.tms.controller.api;

import cn.hutool.core.util.StrUtil;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.api.annotation.RateLimit;
import com.jygjexp.jynx.tms.api.feign.RemoteTmsReturnService;
import com.jygjexp.jynx.tms.constants.ShipModeConstants;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.model.bo.ApiOrder;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.utils.IOTPayQrOrder;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.api.ZdjApiOrder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: chenchang
 * @Description: 中大件与卡派客户订单API接口
 * @Date: 2025/3/23 22:08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/apiOrder")
@Tag(description = "tmsApiOrder", name = "中大件与卡派客户订单API接口")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ApiCustomerController {
    private final TmsCustomerOrderService tmsCustomerOrderService;
    private final TmsOverAreaService tmsOverAreaService;
    private final TmsOrderTrackService tmsOrderTrackService;
    private final TmsOrderTrackNewService tmsOrderTrackNewService;
    private final TmsReceivableService tmsReceivableService;
    private final IOTPayQrOrder payQrOrder;
    private final RemoteTmsReturnService tmsReturnService;
    /**
     * 中大件与卡派客户订单API下单接口
     *
     * @param apiOrder
     * @return
     */
    @Operation(summary = "TMS客户订单API下单接口", description = "TMS客户订单API下单接口")
    @PostMapping("/createApiOrder")
    @Inner(value = false)
    public R createApiOrder(@RequestBody ApiOrder apiOrder) {
        return apiOrder.getShipMode() == ShipModeConstants.SHIP_MODE_KP ? tmsCustomerOrderService.createKpApiOrder(apiOrder)
                : tmsCustomerOrderService.createZdjApiOrder(apiOrder);
    }


    /**
     * 中大件与卡派删除订单接口
     *
     * @return
     */
    @Operation(summary = "中大件与卡派删除订单接口", description = "中大件与卡派删除订单接口")
    @PostMapping("/delete")
    @Inner(value = false)
    public R deleteOrder(@RequestParam String orderNo) {
      return tmsCustomerOrderService.deleteOrder(orderNo);

    }


    /**
     * 中大件与卡派拦截订单接口
     *
     * @return
     */
    @Operation(summary = "中大件与卡派拦截订单接口", description = "中大件与卡派拦截订单接口")
    @PostMapping("/block")
    @Inner(value = false)
    public R blockOrder(@RequestParam String orderNo) {
        return tmsCustomerOrderService.blockOrder(orderNo);
    }


    /**
     * 中大件与卡派获取轨迹接口
     *
     * @return
     */
    @Operation(summary = "中大件与卡派获取轨迹接口", description = "中大件与卡派获取轨迹接口")
    @PostMapping("/track")
    @Inner(value = false)
    public R track(@RequestParam String orderNo) {
        return tmsCustomerOrderService.track(orderNo);
    }

    /**
     * 中大件与卡派批量获取轨迹接口
     *
     * @param orderNos 订单号列表，逗号分隔
     * @return 批量轨迹查询结果
     */
    @Operation(summary = "中大件与卡派批量获取轨迹接口", description = "中大件与卡派批量获取轨迹接口，支持限流")
    @PostMapping("/batchTrack")
    @Inner(value = false)
    @RateLimit(keyPrefix = "batch_track", dimension = "customer", permits = 10, timeWindow = 1, message = "批量轨迹查询请求过于频繁，请稍后重试")
    public R batchTrack(@RequestParam String orderNos) {
        if (StrUtil.isBlank(orderNos)) {
            return R.failed("订单号列表不能为空");
        }

        // 解析订单号列表
        List<String> orderNoList = Arrays.stream(orderNos.split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        return tmsOrderTrackNewService.batchTrackOrders(orderNoList);
    }


    /**
     * 获取全部有效覆盖邮编
     *
     * @return
     */
    @Operation(summary = "获取全部有效覆盖邮编", description = "获取全部有效覆盖邮编")
    @PostMapping("/getZipCode")
    @Inner(value = false)
    public String getZipCode() {
       return tmsOverAreaService.getAllZipCode();
    }


    /**
     * 获取UPS token
     *
     * @return
     */
    @Operation(summary = "获取UPS token", description = "获取UPS token")
    @PostMapping("/getUpsToken")
    @Inner(value = false)
    public String getUpsToken() {
       return tmsOrderTrackService.getAccessTokenWithCache();
    }


    /**
     * 17track查询Nb中大件轨迹
     *
     * @return
     */
    @Operation(summary = "17track查询Nb中大件轨迹", description = "17track查询Nb中大件轨迹")
    @PostMapping("/sq/track")
    @Inner(value = false)
    public R sqTrack(@RequestBody TmsTrackRequest request) {
        return tmsOrderTrackService.getSqTrack(request.getOrderNo());
    }

    /**
     * 小包批量订单轨迹查询（代替原zxoms接口）
     * @return 批量轨迹查询结果
     */
    @Inner(value = false)
    @Operation(summary = "小包批量订单轨迹查询", description = "小包批量订单轨迹查询 - 迁移自zxoms")
    @PostMapping("/tracks")
    public R getZxomsTracks(@RequestBody TrackQueryVo request) {
        return tmsOrderTrackNewService.getTracksFromZxoms(request.getPkgNos(), request.getZip());
    }


    @Operation(summary = "价格计算API", description = "基于订单数据计算基础费用价格(后续需要加上附加费)")
    @PostMapping("/calculatePrice")
    @Inner(value = false)
    public PriceCalculationResultVo calculatePrice(@RequestBody PriceCalculationRequestVo request) {
        return  tmsReceivableService.calculatePrice(request);
    }


    @Operation(summary = "支付接口调试", description = "支付接口调试")
    @PostMapping("/pay")
    @Inner(value = false)
    public void pay(BigDecimal amount, String payType, Long userId) throws Exception {
       // payQrOrder.query("WN20250808064136048064965652");
        payQrOrder.refundOrder(amount,payType,userId);
    }



}

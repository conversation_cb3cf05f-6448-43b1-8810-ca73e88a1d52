package com.jygjexp.jynx.tms.api.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * 限流注解
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {
    
    /**
     * 限流key的前缀
     */
    String keyPrefix() default "rate_limit";
    
    /**
     * 限流维度：customer-按客户维度，global-全局维度
     */
    String dimension() default "customer";
    
    /**
     * 每个时间窗口允许的请求数量
     */
    int permits() default 10;
    
    /**
     * 时间窗口大小
     */
    int timeWindow() default 1;
    
    /**
     * 时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;
    
    /**
     * 限流提示信息
     */
    String message() default "请求过于频繁，请稍后重试";
}

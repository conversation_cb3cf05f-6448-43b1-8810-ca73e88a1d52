package com.jygjexp.jynx.tms.api.handler;

import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.api.exception.RateLimitException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理限流异常
     */
    @ExceptionHandler(RateLimitException.class)
    @ResponseStatus(HttpStatus.TOO_MANY_REQUESTS)
    public R handleRateLimitException(RateLimitException e) {
        log.warn("请求被限流: {}", e.getMessage());
        return R.failed(429, e.getMessage());
    }
}

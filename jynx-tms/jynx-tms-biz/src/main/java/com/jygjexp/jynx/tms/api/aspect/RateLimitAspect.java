package com.jygjexp.jynx.tms.api.aspect;

import cn.hutool.core.util.StrUtil;
import com.jygjexp.jynx.tms.api.annotation.RateLimit;
import com.jygjexp.jynx.tms.api.exception.RateLimitException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * 限流切面
 * 使用滑动窗口算法实现限流
 * 
 * <AUTHOR>
 * @date 2025-09-04
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class RateLimitAspect {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 滑动窗口限流Lua脚本
     * 使用Redis的ZSET数据结构实现滑动窗口
     */
    private static final String SLIDING_WINDOW_SCRIPT = 
        "local key = KEYS[1]\n" +
        "local window = tonumber(ARGV[1])\n" +
        "local limit = tonumber(ARGV[2])\n" +
        "local current = tonumber(ARGV[3])\n" +
        "\n" +
        "-- 清理过期的记录\n" +
        "redis.call('ZREMRANGEBYSCORE', key, 0, current - window * 1000)\n" +
        "\n" +
        "-- 获取当前窗口内的请求数量\n" +
        "local currentCount = redis.call('ZCARD', key)\n" +
        "\n" +
        "-- 检查是否超过限制\n" +
        "if currentCount < limit then\n" +
        "    -- 添加当前请求记录\n" +
        "    redis.call('ZADD', key, current, current)\n" +
        "    -- 设置过期时间\n" +
        "    redis.call('EXPIRE', key, window + 1)\n" +
        "    return {1, limit - currentCount - 1}\n" +
        "else\n" +
        "    return {0, 0}\n" +
        "end";
    
    @Around("@annotation(rateLimit)")
    public Object around(ProceedingJoinPoint point, RateLimit rateLimit) throws Throwable {
        String key = buildKey(rateLimit);
        
        // 执行限流检查
        boolean allowed = checkRateLimit(key, rateLimit);
        
        if (!allowed) {
            log.warn("请求被限流，key: {}, 限制: {}/{}{}", key, rateLimit.permits(), 
                    rateLimit.timeWindow(), rateLimit.timeUnit().name().toLowerCase());
            throw new RateLimitException(rateLimit.message());
        }
        
        return point.proceed();
    }
    
    /**
     * 构建限流key
     */
    private String buildKey(RateLimit rateLimit) {
        StringBuilder keyBuilder = new StringBuilder(rateLimit.keyPrefix());
        
        if ("customer".equals(rateLimit.dimension())) {
            // 按客户维度限流，从请求头获取客户标识
            String customerKey = getCustomerKey();
            if (StrUtil.isNotBlank(customerKey)) {
                keyBuilder.append(":customer:").append(customerKey);
            } else {
                // 如果没有客户标识，使用IP作为标识
                String clientIp = getClientIp();
                keyBuilder.append(":ip:").append(clientIp);
            }
        } else {
            // 全局限流
            keyBuilder.append(":global");
        }
        
        return keyBuilder.toString();
    }
    
    /**
     * 执行限流检查
     */
    private boolean checkRateLimit(String key, RateLimit rateLimit) {
        try {
            DefaultRedisScript<Long[]> script = new DefaultRedisScript<>();
            script.setScriptText(SLIDING_WINDOW_SCRIPT);
            script.setResultType(Long[].class);
            
            long currentTime = System.currentTimeMillis();
            long windowSizeInSeconds = rateLimit.timeUnit().toSeconds(rateLimit.timeWindow());
            
            Long[] result = redisTemplate.execute(script, 
                Collections.singletonList(key),
                String.valueOf(windowSizeInSeconds),
                String.valueOf(rateLimit.permits()),
                String.valueOf(currentTime));
            
            return result != null && result[0] == 1L;
        } catch (Exception e) {
            log.error("限流检查异常，key: {}", key, e);
            // 异常情况下允许请求通过
            return true;
        }
    }
    
    /**
     * 获取客户标识
     * 优先从请求头apiKey获取，其次从其他可能的客户标识字段获取
     */
    private String getCustomerKey() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                
                // 从请求头获取apiKey
                String apiKey = request.getHeader("apiKey");
                if (StrUtil.isNotBlank(apiKey)) {
                    return apiKey;
                }
                
                // 从其他可能的客户标识字段获取
                String customerId = request.getHeader("customerId");
                if (StrUtil.isNotBlank(customerId)) {
                    return customerId;
                }
            }
        } catch (Exception e) {
            log.warn("获取客户标识失败", e);
        }
        return null;
    }
    
    /**
     * 获取客户端IP
     */
    private String getClientIp() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                
                String ip = request.getHeader("X-Forwarded-For");
                if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("X-Real-IP");
                }
                if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getRemoteAddr();
                }
                
                // 处理多个IP的情况，取第一个
                if (StrUtil.isNotBlank(ip) && ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                
                return StrUtil.isNotBlank(ip) ? ip : "unknown";
            }
        } catch (Exception e) {
            log.warn("获取客户端IP失败", e);
        }
        return "unknown";
    }
}
